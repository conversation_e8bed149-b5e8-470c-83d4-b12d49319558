import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'screens/auth_wrapper.dart';
import 'theme/color.dart';
import 'theme/adaptive_theme.dart';
import 'providers/app_provider.dart';
import 'services/sample_data_service.dart';
import 'services/performance_service.dart';
import 'utils/performance_optimizer.dart';
import 'utils/memory_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize performance optimizations first
  await PerformanceService.initialize();
  await PerformanceOptimizer.initialize();
  MemoryManager.initialize();

  // Add error handling for debug builds
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    print('Flutter Error: ${details.exception}');
    print('Stack trace: ${details.stack}');
  };

  try {
    // Initialize Firebase for faster startup
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize Crashlytics
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      FirebaseCrashlytics.instance.recordFlutterFatalError(details);
    };

    print("Firebase initialized successfully");

    // Initialize sample data if needed
    SampleDataService sampleDataService = SampleDataService();
    await sampleDataService.initializeSampleDataIfNeeded();
  } catch (e) {
    print("Firebase initialization error: $e");
    // Don't let Firebase errors prevent app startup
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppProvider(),
      child: Builder(
        builder: (context) => MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'One Touch Hotel',
          theme: AdaptiveTheme.getTheme(context),
          // Optimize navigation
          navigatorObservers: [],
          home: const AuthWrapper(),
          // Performance optimizations
          showPerformanceOverlay: false,
          checkerboardRasterCacheImages: false,
          checkerboardOffscreenLayers: false,
          // Memory optimizations
          builder: (context, child) {
            // Ensure proper text scaling for accessibility
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(
                  MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.2),
                ),
              ),
              child: child!,
            );
          },
        ),
      ),
    );
  }
}
