import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/room.dart';
import '../theme/color.dart';

class RoomCard extends StatelessWidget {
  final Room room;
  final VoidCallback? onBook;

  const RoomCard({
    Key? key,
    required this.room,
    this.onBook,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColor.shadowColor.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Room image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            child: CachedNetworkImage(
              imageUrl: room.images.isNotEmpty ? room.images.first : '',
              height: 180,
              width: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                height: 180,
                color: Colors.grey[200],
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                height: 180,
                color: Colors.grey[200],
                child: const Center(
                  child: Icon(
                    Icons.hotel,
                    size: 48,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ),
          
          // Room details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Room name and type
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            room.name,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColor.textColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            room.type,
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColor.labelColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: room.isAvailable ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        room.isAvailable ? 'Available' : 'Unavailable',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Room features
                Row(
                  children: [
                    _buildFeature(Icons.people, '${room.maxOccupancy} guests'),
                    const SizedBox(width: 16),
                    if (room.size > 0)
                      _buildFeature(Icons.square_foot, '${room.size.toInt()} m²'),
                    if (room.size > 0)
                      const SizedBox(width: 16),
                    if (room.bedType.isNotEmpty)
                      _buildFeature(Icons.bed, room.bedType),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Additional features
                Row(
                  children: [
                    if (room.view.isNotEmpty)
                      _buildFeature(Icons.landscape, room.view),
                    if (room.view.isNotEmpty && room.hasBalcony)
                      const SizedBox(width: 16),
                    if (room.hasBalcony)
                      _buildFeature(Icons.balcony, 'Balcony'),
                    if ((room.view.isNotEmpty || room.hasBalcony) && room.hasKitchen)
                      const SizedBox(width: 16),
                    if (room.hasKitchen)
                      _buildFeature(Icons.kitchen, 'Kitchen'),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Amenities (show first 4)
                if (room.amenities.isNotEmpty)
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: room.amenities.take(4).map((amenity) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColor.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          amenity,
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColor.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                
                const SizedBox(height: 16),
                
                // Description
                if (room.description.isNotEmpty)
                  Text(
                    room.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColor.labelColor,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                
                const SizedBox(height: 16),
                
                // Price and book button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${room.currency} ${room.pricePerNight.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: AppColor.primary,
                          ),
                        ),
                        Text(
                          'per night',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColor.labelColor,
                          ),
                        ),
                      ],
                    ),
                    
                    ElevatedButton(
                      onPressed: room.isAvailable ? onBook : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColor.primary,
                        disabledBackgroundColor: Colors.grey[300],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      ),
                      child: Text(
                        'Book Now',
                        style: TextStyle(
                          color: room.isAvailable ? Colors.white : Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Available rooms count
                if (room.isAvailable && room.availableRooms > 0)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      '${room.availableRooms} room${room.availableRooms > 1 ? 's' : ''} left',
                      style: TextStyle(
                        fontSize: 12,
                        color: room.availableRooms <= 3 ? Colors.red : AppColor.labelColor,
                        fontWeight: room.availableRooms <= 3 ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeature(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColor.labelColor,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: AppColor.labelColor,
          ),
        ),
      ],
    );
  }
}
