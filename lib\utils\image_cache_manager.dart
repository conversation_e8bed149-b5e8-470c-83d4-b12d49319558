import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';

class ImageCacheManager {
  static final ImageCacheManager _instance = ImageCacheManager._internal();
  factory ImageCacheManager() => _instance;
  ImageCacheManager._internal();

  // Cache configuration
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxCacheObjects = 1000;
  static const Duration cacheTimeout = Duration(days: 7);

  // Initialize cache settings
  static void initialize() {
    // Configure image cache
    PaintingBinding.instance.imageCache.maximumSize = maxCacheObjects;
    PaintingBinding.instance.imageCache.maximumSizeBytes = maxCacheSize;
  }

  // Clear all cached images
  static void clearCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  // Clear specific image from cache
  static void clearImageFromCache(String imageUrl) {
    final NetworkImage networkImage = NetworkImage(imageUrl);
    PaintingBinding.instance.imageCache.evict(networkImage);
  }

  // Preload images for better performance
  static Future<void> preloadImages(List<String> imageUrls, BuildContext context) async {
    for (String url in imageUrls) {
      if (url.isNotEmpty) {
        try {
          await precacheImage(NetworkImage(url), context);
        } catch (e) {
          debugPrint('Failed to preload image: $url, Error: $e');
        }
      }
    }
  }

  // Get cache statistics
  static Map<String, dynamic> getCacheStats() {
    final ImageCache cache = PaintingBinding.instance.imageCache;
    return {
      'currentSize': cache.currentSize,
      'currentSizeBytes': cache.currentSizeBytes,
      'maximumSize': cache.maximumSize,
      'maximumSizeBytes': cache.maximumSizeBytes,
      'liveImageCount': cache.liveImageCount,
    };
  }

  // Check if cache needs cleanup
  static bool needsCleanup() {
    final ImageCache cache = PaintingBinding.instance.imageCache;
    final double sizeRatio = cache.currentSizeBytes / cache.maximumSizeBytes;
    final double countRatio = cache.currentSize / cache.maximumSize;
    
    return sizeRatio > 0.8 || countRatio > 0.8;
  }

  // Perform cache cleanup
  static void performCleanup() {
    if (needsCleanup()) {
      final ImageCache cache = PaintingBinding.instance.imageCache;
      
      // Clear half of the cache
      final int targetSize = cache.maximumSize ~/ 2;
      final int targetSizeBytes = cache.maximumSizeBytes ~/ 2;
      
      while (cache.currentSize > targetSize || cache.currentSizeBytes > targetSizeBytes) {
        cache.clearLiveImages();
        if (cache.currentSize <= targetSize && cache.currentSizeBytes <= targetSizeBytes) {
          break;
        }
      }
    }
  }

  // Create optimized cached network image widget
  static Widget buildCachedImage({
    required String imageUrl,
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
    Duration fadeInDuration = const Duration(milliseconds: 300),
    Duration fadeOutDuration = const Duration(milliseconds: 100),
    bool useMemCache = true,
    BorderRadius? borderRadius,
  }) {
    // Calculate memory cache dimensions
    final int memCacheWidth = useMemCache ? width.toInt() : 0;
    final int memCacheHeight = useMemCache ? height.toInt() : 0;

    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      memCacheWidth: memCacheWidth > 0 ? memCacheWidth : null,
      memCacheHeight: memCacheHeight > 0 ? memCacheHeight : null,
      fadeInDuration: fadeInDuration,
      fadeOutDuration: fadeOutDuration,
      placeholder: placeholder ?? _buildDefaultPlaceholder(width, height),
      errorWidget: errorWidget ?? _buildDefaultErrorWidget(width, height),
      useOldImageOnUrlChange: true,
      filterQuality: FilterQuality.medium,
    );

    // Apply border radius if specified
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  // Default placeholder widget
  static Widget _buildDefaultPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: width > 100 ? 24 : 16,
              height: width > 100 ? 24 : 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
              ),
            ),
            if (width > 100) ...[
              const SizedBox(height: 8),
              Text(
                'Loading...',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Default error widget
  static Widget _buildDefaultErrorWidget(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              color: Colors.grey[500],
              size: width > 100 ? 48 : 24,
            ),
            if (width > 100) ...[
              const SizedBox(height: 8),
              Text(
                'Image not available',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Build image with fallback options
  static Widget buildImageWithFallback({
    required String primaryUrl,
    List<String> fallbackUrls = const [],
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    return _ImageWithFallback(
      primaryUrl: primaryUrl,
      fallbackUrls: fallbackUrls,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
    );
  }

  // Monitor cache performance
  static void startCacheMonitoring() {
    // This would typically run in a background isolate
    // For now, we'll just provide the monitoring capability
    debugPrint('Cache monitoring started');
  }

  // Get memory usage information
  static Map<String, dynamic> getMemoryUsage() {
    final ImageCache cache = PaintingBinding.instance.imageCache;
    return {
      'cacheSize': cache.currentSizeBytes,
      'cacheSizeMB': (cache.currentSizeBytes / (1024 * 1024)).toStringAsFixed(2),
      'cacheCount': cache.currentSize,
      'liveImages': cache.liveImageCount,
    };
  }

  // Optimize cache for low memory devices
  static void optimizeForLowMemory() {
    final ImageCache cache = PaintingBinding.instance.imageCache;
    
    // Reduce cache size for low memory devices
    cache.maximumSize = 50; // Reduced from default
    cache.maximumSizeBytes = 20 * 1024 * 1024; // 20MB instead of 100MB
    
    // Clear current cache
    cache.clear();
    cache.clearLiveImages();
  }
}

// Widget for handling image fallbacks
class _ImageWithFallback extends StatefulWidget {
  final String primaryUrl;
  final List<String> fallbackUrls;
  final double width;
  final double height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  const _ImageWithFallback({
    required this.primaryUrl,
    required this.fallbackUrls,
    required this.width,
    required this.height,
    required this.fit,
    this.borderRadius,
  });

  @override
  _ImageWithFallbackState createState() => _ImageWithFallbackState();
}

class _ImageWithFallbackState extends State<_ImageWithFallback> {
  int _currentUrlIndex = 0;
  List<String> _allUrls = [];

  @override
  void initState() {
    super.initState();
    _allUrls = [widget.primaryUrl, ...widget.fallbackUrls];
  }

  void _tryNextUrl() {
    if (_currentUrlIndex < _allUrls.length - 1) {
      setState(() {
        _currentUrlIndex++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_currentUrlIndex >= _allUrls.length) {
      return ImageCacheManager._buildDefaultErrorWidget(widget.width, widget.height);
    }

    return ImageCacheManager.buildCachedImage(
      imageUrl: _allUrls[_currentUrlIndex],
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      borderRadius: widget.borderRadius,
      errorWidget: (context, url, error) {
        // Try next URL on error
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _tryNextUrl();
        });
        return ImageCacheManager._buildDefaultPlaceholder(widget.width, widget.height);
      },
    );
  }
}
