import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'image_cache_manager.dart';
import 'image_optimizer.dart';

class AdaptiveImageLoader {
  static final AdaptiveImageLoader _instance = AdaptiveImageLoader._internal();
  factory AdaptiveImageLoader() => _instance;
  AdaptiveImageLoader._internal();

  // Device performance levels
  static DevicePerformance? _devicePerformance;
  static NetworkQuality _networkQuality = NetworkQuality.good;

  // Initialize adaptive loading
  static Future<void> initialize() async {
    _devicePerformance = await _detectDevicePerformance();
    _networkQuality = await _detectNetworkQuality();
  }

  // Detect device performance level
  static Future<DevicePerformance> _detectDevicePerformance() async {
    try {
      // Get device info
      final double pixelRatio = WidgetsBinding.instance.platformDispatcher.views.first.devicePixelRatio;
      final Size screenSize = WidgetsBinding.instance.platformDispatcher.views.first.physicalSize;
      
      // Calculate screen area
      final double screenArea = screenSize.width * screenSize.height;
      
      // Determine performance based on screen size and pixel ratio
      if (screenArea > 2000000 && pixelRatio >= 3.0) {
        return DevicePerformance.high;
      } else if (screenArea > 1000000 && pixelRatio >= 2.0) {
        return DevicePerformance.medium;
      } else {
        return DevicePerformance.low;
      }
    } catch (e) {
      // Default to medium performance if detection fails
      return DevicePerformance.medium;
    }
  }

  // Detect network quality (simplified)
  static Future<NetworkQuality> _detectNetworkQuality() async {
    // In a real implementation, you would check actual network speed
    // For now, we'll assume good quality
    return NetworkQuality.good;
  }

  // Get optimal image configuration
  static ImageConfig getOptimalConfig({
    required double displayWidth,
    required double displayHeight,
    required ImageType imageType,
    bool isListItem = false,
  }) {
    final DevicePerformance performance = _devicePerformance ?? DevicePerformance.medium;
    final NetworkQuality network = _networkQuality;

    // Base configuration
    ImageConfig config = ImageConfig(
      width: displayWidth.toInt(),
      height: displayHeight.toInt(),
      quality: 80,
      useMemCache: true,
      enableFadeAnimation: true,
      cacheWidth: displayWidth.toInt(),
      cacheHeight: displayHeight.toInt(),
    );

    // Adjust based on device performance
    switch (performance) {
      case DevicePerformance.high:
        config = config.copyWith(
          quality: imageType == ImageType.thumbnail ? 85 : 90,
          useMemCache: true,
          enableFadeAnimation: true,
        );
        break;
      case DevicePerformance.medium:
        config = config.copyWith(
          quality: imageType == ImageType.thumbnail ? 75 : 80,
          useMemCache: true,
          enableFadeAnimation: true,
        );
        break;
      case DevicePerformance.low:
        config = config.copyWith(
          quality: imageType == ImageType.thumbnail ? 60 : 70,
          useMemCache: false,
          enableFadeAnimation: false,
          cacheWidth: (displayWidth * 0.8).toInt(),
          cacheHeight: (displayHeight * 0.8).toInt(),
        );
        break;
    }

    // Adjust based on network quality
    switch (network) {
      case NetworkQuality.poor:
        config = config.copyWith(
          quality: config.quality - 20,
          cacheWidth: (config.cacheWidth * 0.7).toInt(),
          cacheHeight: (config.cacheHeight * 0.7).toInt(),
        );
        break;
      case NetworkQuality.fair:
        config = config.copyWith(
          quality: config.quality - 10,
          cacheWidth: (config.cacheWidth * 0.85).toInt(),
          cacheHeight: (config.cacheHeight * 0.85).toInt(),
        );
        break;
      case NetworkQuality.good:
        // Keep default settings
        break;
    }

    // Special handling for list items
    if (isListItem) {
      config = config.copyWith(
        quality: config.quality - 10,
        enableFadeAnimation: false,
      );
    }

    return config;
  }

  // Build adaptive image widget
  static Widget buildAdaptiveImage({
    required String imageUrl,
    required double width,
    required double height,
    ImageType imageType = ImageType.general,
    bool isListItem = false,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Widget? placeholder,
    Widget? errorWidget,
    List<String> fallbackUrls = const [],
  }) {
    final ImageConfig config = getOptimalConfig(
      displayWidth: width,
      displayHeight: height,
      imageType: imageType,
      isListItem: isListItem,
    );

    return _AdaptiveImageWidget(
      imageUrl: imageUrl,
      fallbackUrls: fallbackUrls,
      width: width,
      height: height,
      fit: fit,
      config: config,
      borderRadius: borderRadius,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }

  // Preload images with adaptive quality
  static Future<void> preloadAdaptiveImages({
    required List<String> imageUrls,
    required BuildContext context,
    ImageType imageType = ImageType.general,
  }) async {
    final DevicePerformance performance = _devicePerformance ?? DevicePerformance.medium;
    
    // Limit preloading based on device performance
    int maxPreload;
    switch (performance) {
      case DevicePerformance.high:
        maxPreload = 10;
        break;
      case DevicePerformance.medium:
        maxPreload = 5;
        break;
      case DevicePerformance.low:
        maxPreload = 3;
        break;
    }

    final List<String> urlsToPreload = imageUrls.take(maxPreload).toList();
    
    for (String url in urlsToPreload) {
      if (url.isNotEmpty) {
        try {
          await precacheImage(NetworkImage(url), context);
        } catch (e) {
          debugPrint('Failed to preload image: $url');
        }
      }
    }
  }

  // Get current performance info
  static Map<String, dynamic> getPerformanceInfo() {
    return {
      'devicePerformance': _devicePerformance?.toString() ?? 'unknown',
      'networkQuality': _networkQuality.toString(),
      'isInitialized': _devicePerformance != null,
    };
  }

  // Update network quality (can be called when network conditions change)
  static void updateNetworkQuality(NetworkQuality quality) {
    _networkQuality = quality;
  }
}

// Adaptive image widget
class _AdaptiveImageWidget extends StatefulWidget {
  final String imageUrl;
  final List<String> fallbackUrls;
  final double width;
  final double height;
  final BoxFit fit;
  final ImageConfig config;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;

  const _AdaptiveImageWidget({
    required this.imageUrl,
    required this.fallbackUrls,
    required this.width,
    required this.height,
    required this.fit,
    required this.config,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<_AdaptiveImageWidget> createState() => _AdaptiveImageWidgetState();
}

class _AdaptiveImageWidgetState extends State<_AdaptiveImageWidget> {
  int _currentUrlIndex = 0;
  List<String> _allUrls = [];

  @override
  void initState() {
    super.initState();
    _allUrls = [widget.imageUrl, ...widget.fallbackUrls];
  }

  void _tryNextUrl() {
    if (_currentUrlIndex < _allUrls.length - 1) {
      setState(() {
        _currentUrlIndex++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_currentUrlIndex >= _allUrls.length || _allUrls[_currentUrlIndex].isEmpty) {
      return widget.errorWidget ?? _buildDefaultErrorWidget();
    }

    Widget imageWidget = CachedNetworkImage(
      imageUrl: _allUrls[_currentUrlIndex],
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      memCacheWidth: widget.config.useMemCache ? widget.config.cacheWidth : null,
      memCacheHeight: widget.config.useMemCache ? widget.config.cacheHeight : null,
      fadeInDuration: widget.config.enableFadeAnimation 
          ? const Duration(milliseconds: 300) 
          : Duration.zero,
      fadeOutDuration: widget.config.enableFadeAnimation 
          ? const Duration(milliseconds: 100) 
          : Duration.zero,
      placeholder: widget.placeholder ?? _buildDefaultPlaceholder(),
      errorWidget: (context, url, error) {
        // Try next URL on error
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _tryNextUrl();
        });
        return widget.placeholder ?? _buildDefaultPlaceholder();
      },
      filterQuality: _getFilterQuality(),
    );

    // Apply border radius if specified
    if (widget.borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  FilterQuality _getFilterQuality() {
    final DevicePerformance performance = AdaptiveImageLoader._devicePerformance ?? DevicePerformance.medium;
    
    switch (performance) {
      case DevicePerformance.high:
        return FilterQuality.high;
      case DevicePerformance.medium:
        return FilterQuality.medium;
      case DevicePerformance.low:
        return FilterQuality.low;
    }
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: Center(
        child: widget.config.enableFadeAnimation
            ? CircularProgressIndicator(strokeWidth: 2)
            : Icon(Icons.image, color: Colors.grey[400]),
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[300],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.broken_image, color: Colors.grey[500], size: 32),
            if (widget.width > 100) ...[
              const SizedBox(height: 8),
              Text(
                'Image not available',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Data classes
enum DevicePerformance { low, medium, high }
enum NetworkQuality { poor, fair, good }
enum ImageType { thumbnail, general, gallery, fullscreen }

class ImageConfig {
  final int width;
  final int height;
  final int quality;
  final bool useMemCache;
  final bool enableFadeAnimation;
  final int cacheWidth;
  final int cacheHeight;

  const ImageConfig({
    required this.width,
    required this.height,
    required this.quality,
    required this.useMemCache,
    required this.enableFadeAnimation,
    required this.cacheWidth,
    required this.cacheHeight,
  });

  ImageConfig copyWith({
    int? width,
    int? height,
    int? quality,
    bool? useMemCache,
    bool? enableFadeAnimation,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return ImageConfig(
      width: width ?? this.width,
      height: height ?? this.height,
      quality: quality ?? this.quality,
      useMemCache: useMemCache ?? this.useMemCache,
      enableFadeAnimation: enableFadeAnimation ?? this.enableFadeAnimation,
      cacheWidth: cacheWidth ?? this.cacheWidth,
      cacheHeight: cacheHeight ?? this.cacheHeight,
    );
  }
}
