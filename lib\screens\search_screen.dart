import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hotel.dart';
import '../services/hotel_service.dart';
import '../theme/color.dart';
import '../widgets/hotel_card.dart';
import '../widgets/search_filters.dart';
import 'hotel_detail_screen.dart';

class SearchScreen extends StatefulWidget {
  final String? initialCity;
  final String? initialQuery;

  const SearchScreen({
    Key? key,
    this.initialCity,
    this.initialQuery,
  }) : super(key: key);

  @override
  _SearchScreenState createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final HotelService _hotelService = HotelService();
  
  List<Hotel> _hotels = [];
  List<Hotel> _filteredHotels = [];
  bool _isLoading = false;
  bool _showFilters = false;
  
  // Filter parameters
  String? _selectedCity;
  double? _minRating;
  double? _maxPrice;
  List<String> _selectedAmenities = [];
  
  final List<String> _availableAmenities = [
    'WiFi',
    'Pool',
    'Gym',
    'Spa',
    'Restaurant',
    'Bar',
    'Room Service',
    'Parking',
    'Pet Friendly',
    'Business Center',
    'Laundry',
    'Airport Shuttle',
  ];

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.initialQuery ?? '';
    _selectedCity = widget.initialCity;
    _searchHotels();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _searchHotels() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<Hotel> hotels = await _hotelService.searchHotels(
        query: _searchController.text.trim().isEmpty ? null : _searchController.text.trim(),
        city: _selectedCity,
        minRating: _minRating,
        maxPrice: _maxPrice,
        amenities: _selectedAmenities.isEmpty ? null : _selectedAmenities,
      );

      setState(() {
        _hotels = hotels;
        _filteredHotels = hotels;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error searching hotels: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _applyFilters() {
    setState(() {
      _showFilters = false;
    });
    _searchHotels();
  }

  void _clearFilters() {
    setState(() {
      _selectedCity = null;
      _minRating = null;
      _maxPrice = null;
      _selectedAmenities.clear();
      _showFilters = false;
    });
    _searchHotels();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.appBgColor,
      appBar: AppBar(
        backgroundColor: AppColor.appBarColor,
        elevation: 0,
        title: Text(
          'Search Hotels',
          style: TextStyle(
            color: AppColor.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColor.textColor),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: _hasActiveFilters() ? AppColor.primary : AppColor.textColor,
            ),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            color: AppColor.appBarColor,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search hotels...',
                prefixIcon: Icon(Icons.search, color: AppColor.primary),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, color: AppColor.labelColor),
                        onPressed: () {
                          _searchController.clear();
                          _searchHotels();
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onSubmitted: (_) => _searchHotels(),
            ),
          ),

          // Filters panel
          if (_showFilters)
            SearchFilters(
              selectedCity: _selectedCity,
              minRating: _minRating,
              maxPrice: _maxPrice,
              selectedAmenities: _selectedAmenities,
              availableAmenities: _availableAmenities,
              onCityChanged: (city) => setState(() => _selectedCity = city),
              onMinRatingChanged: (rating) => setState(() => _minRating = rating),
              onMaxPriceChanged: (price) => setState(() => _maxPrice = price),
              onAmenitiesChanged: (amenities) => setState(() => _selectedAmenities = amenities),
              onApplyFilters: _applyFilters,
              onClearFilters: _clearFilters,
            ),

          // Results
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredHotels.isEmpty
                    ? _buildEmptyState()
                    : _buildHotelList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColor.labelColor,
          ),
          const SizedBox(height: 16),
          Text(
            'No hotels found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColor.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search criteria',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.labelColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _clearFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Clear Filters',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHotelList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredHotels.length,
      itemBuilder: (context, index) {
        Hotel hotel = _filteredHotels[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: HotelCard(
            hotel: hotel,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HotelDetailScreen(hotel: hotel),
                ),
              );
            },
          ),
        );
      },
    );
  }

  bool _hasActiveFilters() {
    return _selectedCity != null ||
           _minRating != null ||
           _maxPrice != null ||
           _selectedAmenities.isNotEmpty;
  }
}
