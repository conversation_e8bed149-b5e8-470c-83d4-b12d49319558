import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;

class ImageUploadService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final ImagePicker _imagePicker = ImagePicker();

  // Upload profile picture
  Future<String?> uploadProfilePicture({
    required String userId,
    required ImageSource source,
  }) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image == null) return null;

      // Create unique filename
      final String fileName = 'profile_${userId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String filePath = 'users/$userId/profile/$fileName';

      // Upload to Firebase Storage
      final Reference ref = _storage.ref().child(filePath);
      
      String downloadUrl;
      if (kIsWeb) {
        // Web upload
        final Uint8List imageData = await image.readAsBytes();
        final UploadTask uploadTask = ref.putData(
          imageData,
          SettableMetadata(contentType: 'image/jpeg'),
        );
        final TaskSnapshot snapshot = await uploadTask;
        downloadUrl = await snapshot.ref.getDownloadURL();
      } else {
        // Mobile upload
        final File imageFile = File(image.path);
        final UploadTask uploadTask = ref.putFile(
          imageFile,
          SettableMetadata(contentType: 'image/jpeg'),
        );
        final TaskSnapshot snapshot = await uploadTask;
        downloadUrl = await snapshot.ref.getDownloadURL();
      }

      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading profile picture: $e');
      throw 'Failed to upload profile picture: $e';
    }
  }

  // Upload review photos
  Future<List<String>> uploadReviewPhotos({
    required String userId,
    required String reviewId,
    required List<XFile> images,
  }) async {
    try {
      List<String> downloadUrls = [];

      for (int i = 0; i < images.length; i++) {
        final XFile image = images[i];
        
        // Create unique filename
        final String fileName = 'review_${reviewId}_${i}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final String filePath = 'reviews/$reviewId/photos/$fileName';

        // Upload to Firebase Storage
        final Reference ref = _storage.ref().child(filePath);
        
        String downloadUrl;
        if (kIsWeb) {
          // Web upload
          final Uint8List imageData = await image.readAsBytes();
          final UploadTask uploadTask = ref.putData(
            imageData,
            SettableMetadata(contentType: 'image/jpeg'),
          );
          final TaskSnapshot snapshot = await uploadTask;
          downloadUrl = await snapshot.ref.getDownloadURL();
        } else {
          // Mobile upload
          final File imageFile = File(image.path);
          final UploadTask uploadTask = ref.putFile(
            imageFile,
            SettableMetadata(contentType: 'image/jpeg'),
          );
          final TaskSnapshot snapshot = await uploadTask;
          downloadUrl = await snapshot.ref.getDownloadURL();
        }

        downloadUrls.add(downloadUrl);
      }

      return downloadUrls;
    } catch (e) {
      debugPrint('Error uploading review photos: $e');
      throw 'Failed to upload review photos: $e';
    }
  }

  // Pick multiple images for reviews
  Future<List<XFile>> pickMultipleImages({int maxImages = 5}) async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      // Limit to maxImages
      if (images.length > maxImages) {
        return images.take(maxImages).toList();
      }

      return images;
    } catch (e) {
      debugPrint('Error picking images: $e');
      throw 'Failed to pick images: $e';
    }
  }

  // Pick single image
  Future<XFile?> pickSingleImage({
    required ImageSource source,
    int maxWidth = 1024,
    int maxHeight = 1024,
    int imageQuality = 80,
  }) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: imageQuality,
      );

      return image;
    } catch (e) {
      debugPrint('Error picking image: $e');
      throw 'Failed to pick image: $e';
    }
  }

  // Delete image from Firebase Storage
  Future<void> deleteImage(String imageUrl) async {
    try {
      final Reference ref = _storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      debugPrint('Error deleting image: $e');
      // Don't throw error for deletion failures
    }
  }

  // Show image source selection dialog
  Future<ImageSource?> showImageSourceDialog(BuildContext context) async {
    return showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Image Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () => Navigator.pop(context, ImageSource.camera),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
            ],
          ),
        );
      },
    );
  }

  // Compress image for better performance
  Future<Uint8List?> compressImage(XFile image, {int quality = 80}) async {
    try {
      final Uint8List imageData = await image.readAsBytes();
      
      // For now, return original data
      // In production, you might want to use image compression libraries
      return imageData;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return null;
    }
  }

  // Validate image file
  bool isValidImageFile(XFile file) {
    final String extension = path.extension(file.path).toLowerCase();
    const List<String> validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    return validExtensions.contains(extension);
  }

  // Get image file size in MB
  Future<double> getImageSizeInMB(XFile file) async {
    try {
      final int bytes = await file.length();
      return bytes / (1024 * 1024); // Convert to MB
    } catch (e) {
      return 0.0;
    }
  }

  // Check if image size is within limit
  Future<bool> isImageSizeValid(XFile file, {double maxSizeMB = 5.0}) async {
    final double sizeMB = await getImageSizeInMB(file);
    return sizeMB <= maxSizeMB;
  }
}
