import 'package:flutter/material.dart';
import '../theme/color.dart';

class SearchFilters extends StatefulWidget {
  final String? selectedCity;
  final double? minRating;
  final double? maxPrice;
  final List<String> selectedAmenities;
  final List<String> availableAmenities;
  final Function(String?) onCityChanged;
  final Function(double?) onMinRatingChanged;
  final Function(double?) onMaxPriceChanged;
  final Function(List<String>) onAmenitiesChanged;
  final VoidCallback onApplyFilters;
  final VoidCallback onClearFilters;

  const SearchFilters({
    Key? key,
    this.selectedCity,
    this.minRating,
    this.maxPrice,
    required this.selectedAmenities,
    required this.availableAmenities,
    required this.onCityChanged,
    required this.onMinRatingChanged,
    required this.onMaxPriceChanged,
    required this.onAmenitiesChanged,
    required this.onApplyFilters,
    required this.onClearFilters,
  }) : super(key: key);

  @override
  _SearchFiltersState createState() => _SearchFiltersState();
}

class _SearchFiltersState extends State<SearchFilters> {
  final List<String> _cities = [
    'Mumbai',
    'Delhi',
    'Bangalore',
    'Chennai',
    'Kolkata',
    'Hyderabad',
    'Pune',
    'Jaipur',
    'Goa',
    'Shimla',
    'Udaipur',
    'Agra',
    'Kochi',
    'Mysore',
    'Rishikesh',
    'Tiruvannamalai',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColor.textColor,
                  ),
                ),
                TextButton(
                  onPressed: widget.onClearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: AppColor.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // City filter
                  _buildSectionTitle('City'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: widget.selectedCity,
                    decoration: InputDecoration(
                      hintText: 'Select city',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: _cities.map((city) {
                      return DropdownMenuItem(
                        value: city,
                        child: Text(city),
                      );
                    }).toList(),
                    onChanged: widget.onCityChanged,
                  ),
                  const SizedBox(height: 24),
                  
                  // Rating filter
                  _buildSectionTitle('Minimum Rating'),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Slider(
                          value: widget.minRating ?? 0.0,
                          min: 0.0,
                          max: 5.0,
                          divisions: 10,
                          activeColor: AppColor.primary,
                          label: widget.minRating?.toStringAsFixed(1) ?? '0.0',
                          onChanged: widget.onMinRatingChanged,
                        ),
                      ),
                      Container(
                        width: 60,
                        child: Text(
                          '${widget.minRating?.toStringAsFixed(1) ?? '0.0'} ⭐',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: AppColor.textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  
                  // Price filter
                  _buildSectionTitle('Maximum Price per Night'),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Slider(
                          value: widget.maxPrice ?? 500.0,
                          min: 0.0,
                          max: 1000.0,
                          divisions: 20,
                          activeColor: AppColor.primary,
                          label: '\$${widget.maxPrice?.toStringAsFixed(0) ?? '500'}',
                          onChanged: widget.onMaxPriceChanged,
                        ),
                      ),
                      Container(
                        width: 80,
                        child: Text(
                          '\$${widget.maxPrice?.toStringAsFixed(0) ?? '500'}',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: AppColor.textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  
                  // Amenities filter
                  _buildSectionTitle('Amenities'),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: widget.availableAmenities.map((amenity) {
                      bool isSelected = widget.selectedAmenities.contains(amenity);
                      return FilterChip(
                        label: Text(amenity),
                        selected: isSelected,
                        onSelected: (selected) {
                          List<String> newAmenities = List.from(widget.selectedAmenities);
                          if (selected) {
                            newAmenities.add(amenity);
                          } else {
                            newAmenities.remove(amenity);
                          }
                          widget.onAmenitiesChanged(newAmenities);
                        },
                        selectedColor: AppColor.primary.withValues(alpha: 0.2),
                        checkmarkColor: AppColor.primary,
                        labelStyle: TextStyle(
                          color: isSelected ? AppColor.primary : AppColor.textColor,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
          
          // Apply button
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: widget.onApplyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Apply Filters',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
    );
  }
}
