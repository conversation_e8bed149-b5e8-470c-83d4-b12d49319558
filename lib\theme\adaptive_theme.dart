import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'color.dart';
import '../utils/responsive_helper.dart';

class AdaptiveTheme {
  // Get theme based on device performance and screen size
  static ThemeData getTheme(BuildContext context) {
    final performance = ResponsiveHelper.getDevicePerformance(context);
    final isMobile = ResponsiveHelper.isMobile(context);
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColor.primary,
        brightness: Brightness.light,
      ),
      
      // Adaptive font sizes
      textTheme: _getAdaptiveTextTheme(context, performance),
      
      // Adaptive app bar theme
      appBarTheme: _getAdaptiveAppBarTheme(context, performance),
      
      // Adaptive card theme
      cardTheme: _getAdaptiveCardTheme(context, performance),
      
      // Adaptive button themes
      elevatedButtonTheme: _getAdaptiveElevatedButtonTheme(context, performance),
      textButtonTheme: _getAdaptiveTextButtonTheme(context, performance),
      outlinedButtonTheme: _getAdaptiveOutlinedButtonTheme(context, performance),
      
      // Adaptive input decoration
      inputDecorationTheme: _getAdaptiveInputTheme(context, performance),
      
      // Adaptive list tile theme
      listTileTheme: _getAdaptiveListTileTheme(context, performance),
      
      // Adaptive bottom navigation
      bottomNavigationBarTheme: _getAdaptiveBottomNavTheme(context, performance),
      
      // Adaptive tab bar theme
      tabBarTheme: _getAdaptiveTabBarTheme(context, performance),
      
      // Adaptive dialog theme
      dialogTheme: _getAdaptiveDialogTheme(context, performance),
      
      // Adaptive chip theme
      chipTheme: _getAdaptiveChipTheme(context, performance),
      
      // Performance-based visual density
      visualDensity: performance == DevicePerformance.low 
          ? VisualDensity.compact 
          : VisualDensity.standard,
      
      // Adaptive splash factory
      splashFactory: performance == DevicePerformance.low 
          ? NoSplash.splashFactory 
          : InkRipple.splashFactory,
      
      // Platform-specific adjustments
      platform: TargetPlatform.android,
    );
  }

  static TextTheme _getAdaptiveTextTheme(BuildContext context, DevicePerformance performance) {
    final baseSize = performance == DevicePerformance.low ? 0.9 : 1.0;
    
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 32, tablet: 36, desktop: 40) * baseSize,
        fontWeight: FontWeight.bold,
        color: AppColor.textColor,
      ),
      displayMedium: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 28, tablet: 32, desktop: 36) * baseSize,
        fontWeight: FontWeight.bold,
        color: AppColor.textColor,
      ),
      displaySmall: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 24, tablet: 28, desktop: 32) * baseSize,
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      headlineLarge: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 22, tablet: 26, desktop: 30) * baseSize,
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      headlineMedium: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 20, tablet: 24, desktop: 28) * baseSize,
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      headlineSmall: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 18, tablet: 22, desktop: 26) * baseSize,
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      titleLarge: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 16, tablet: 20, desktop: 24) * baseSize,
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      titleMedium: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 18, desktop: 22) * baseSize,
        fontWeight: FontWeight.w500,
        color: AppColor.textColor,
      ),
      titleSmall: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 12, tablet: 16, desktop: 20) * baseSize,
        fontWeight: FontWeight.w500,
        color: AppColor.textColor,
      ),
      bodyLarge: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18) * baseSize,
        color: AppColor.textColor,
      ),
      bodyMedium: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 12, tablet: 14, desktop: 16) * baseSize,
        color: AppColor.textColor,
      ),
      bodySmall: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 10, tablet: 12, desktop: 14) * baseSize,
        color: AppColor.labelColor,
      ),
      labelLarge: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 12, tablet: 14, desktop: 16) * baseSize,
        fontWeight: FontWeight.w500,
        color: AppColor.textColor,
      ),
      labelMedium: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 10, tablet: 12, desktop: 14) * baseSize,
        fontWeight: FontWeight.w500,
        color: AppColor.labelColor,
      ),
      labelSmall: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 8, tablet: 10, desktop: 12) * baseSize,
        fontWeight: FontWeight.w500,
        color: AppColor.labelColor,
      ),
    );
  }

  static AppBarTheme _getAdaptiveAppBarTheme(BuildContext context, DevicePerformance performance) {
    return AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: AppColor.textColor,
      elevation: performance == DevicePerformance.low ? 1 : 2,
      shadowColor: AppColor.shadowColor.withValues(alpha: 0.1),
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 18, tablet: 20, desktop: 22),
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  static CardTheme _getAdaptiveCardTheme(BuildContext context, DevicePerformance performance) {
    return CardTheme(
      elevation: performance == DevicePerformance.low ? 1 : 2,
      shadowColor: AppColor.shadowColor.withValues(alpha: 0.1),
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 8 : 12,
        ),
      ),
      margin: EdgeInsets.all(
        performance == DevicePerformance.low ? 6 : 8,
      ),
    );
  }

  static ElevatedButtonThemeData _getAdaptiveElevatedButtonTheme(BuildContext context, DevicePerformance performance) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColor.primary,
        foregroundColor: Colors.white,
        elevation: performance == DevicePerformance.low ? 1 : 2,
        minimumSize: Size(0, ResponsiveHelper.getButtonHeight(context)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            performance == DevicePerformance.low ? 8 : 12,
          ),
        ),
        textStyle: TextStyle(
          fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  static TextButtonThemeData _getAdaptiveTextButtonTheme(BuildContext context, DevicePerformance performance) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColor.primary,
        minimumSize: Size(0, ResponsiveHelper.getButtonHeight(context)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            performance == DevicePerformance.low ? 8 : 12,
          ),
        ),
        textStyle: TextStyle(
          fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  static OutlinedButtonThemeData _getAdaptiveOutlinedButtonTheme(BuildContext context, DevicePerformance performance) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColor.primary,
        side: BorderSide(color: AppColor.primary),
        minimumSize: Size(0, ResponsiveHelper.getButtonHeight(context)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            performance == DevicePerformance.low ? 8 : 12,
          ),
        ),
        textStyle: TextStyle(
          fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  static InputDecorationTheme _getAdaptiveInputTheme(BuildContext context, DevicePerformance performance) {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 8 : 12,
        ),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 8 : 12,
        ),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 8 : 12,
        ),
        borderSide: BorderSide(color: AppColor.primary, width: 2),
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: performance == DevicePerformance.low ? 12 : 16,
        vertical: performance == DevicePerformance.low ? 12 : 16,
      ),
    );
  }

  static ListTileThemeData _getAdaptiveListTileTheme(BuildContext context, DevicePerformance performance) {
    return ListTileThemeData(
      contentPadding: EdgeInsets.symmetric(
        horizontal: performance == DevicePerformance.low ? 12 : 16,
        vertical: performance == DevicePerformance.low ? 4 : 8,
      ),
      minVerticalPadding: performance == DevicePerformance.low ? 4 : 8,
    );
  }

  static BottomNavigationBarThemeData _getAdaptiveBottomNavTheme(BuildContext context, DevicePerformance performance) {
    return BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: AppColor.primary,
      unselectedItemColor: AppColor.labelColor,
      elevation: performance == DevicePerformance.low ? 4 : 8,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 10, tablet: 12, desktop: 14),
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 10, tablet: 12, desktop: 14),
        fontWeight: FontWeight.normal,
      ),
    );
  }

  static TabBarTheme _getAdaptiveTabBarTheme(BuildContext context, DevicePerformance performance) {
    return TabBarTheme(
      labelColor: AppColor.primary,
      unselectedLabelColor: AppColor.labelColor,
      indicatorColor: AppColor.primary,
      labelStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18),
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18),
        fontWeight: FontWeight.normal,
      ),
    );
  }

  static DialogTheme _getAdaptiveDialogTheme(BuildContext context, DevicePerformance performance) {
    return DialogTheme(
      elevation: performance == DevicePerformance.low ? 4 : 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 12 : 16,
        ),
      ),
      titleTextStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 18, tablet: 20, desktop: 22),
        fontWeight: FontWeight.w600,
        color: AppColor.textColor,
      ),
      contentTextStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 14, tablet: 16, desktop: 18),
        color: AppColor.textColor,
      ),
    );
  }

  static ChipThemeData _getAdaptiveChipTheme(BuildContext context, DevicePerformance performance) {
    return ChipThemeData(
      backgroundColor: Colors.grey.shade100,
      selectedColor: AppColor.primary.withValues(alpha: 0.2),
      labelStyle: TextStyle(
        fontSize: ResponsiveHelper.responsiveValue(context, mobile: 12, tablet: 14, desktop: 16),
        color: AppColor.textColor,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          performance == DevicePerformance.low ? 16 : 20,
        ),
      ),
      elevation: performance == DevicePerformance.low ? 0 : 1,
    );
  }
}
